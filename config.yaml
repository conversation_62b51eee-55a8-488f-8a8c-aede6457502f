apps: []
inbounds:
  '11111':
    type: mock
    id: '11111'
    interval: '1'
    data_type: sensor
    error_rate: '0.1'
outbounds:
  print:
    type: custom_ioteq
    id: print
rules:
- name: status
  rules:
  - 
    source: $
    target_name: None
    target: $
    type: reference
  - source_name: None
    source: $.id
    target_name: None
    target: $.id
    type: reference
  - source_name: None
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: None
    target_name: None
    target: $.CreateDate
    type: expr
  - source_name: None
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: None
    target_name: None
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
flows:
- name: flow01
  inbounds:
  - '1'
  outbound: custom_ioteq
  rules:
  - name: status
    trigger: None
    action: IOTEqStatues
    merge_strategy: None
server_port: '9999'
version: '1'
