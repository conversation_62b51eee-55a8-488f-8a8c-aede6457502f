#!/usr/bin/env python3
"""测试 outbound 配置类型的脚本"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_outbound_types():
    """测试 OutboundTypes 枚举"""
    try:
        from beezer.type_model import OutboundTypes
        print("✓ OutboundTypes 导入成功")
        print(f"  支持的类型: {[t.value for t in OutboundTypes]}")
        return True
    except Exception as e:
        print(f"✗ OutboundTypes 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_print_config():
    """测试 PrintOutboundConfig"""
    try:
        from beezer.type_model import PrintOutboundConfig
        
        config_data = {
            'type': 'print',
            'id': 'test_print',
            'format': 'json'
        }
        
        validated = PrintOutboundConfig.model_validate(config_data)
        print("✓ PrintOutboundConfig 验证成功")
        print(f"  配置: {validated.model_dump()}")
        return True
    except Exception as e:
        print(f"✗ PrintOutboundConfig 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_model():
    """测试 ConfigModel"""
    try:
        from beezer.type_model import ConfigModel
        
        config_data = {
            'apps': [],
            'inbounds': {},
            'outbounds': {
                'test_print': {
                    'type': 'print',
                    'id': 'test_print',
                    'format': 'json'
                }
            },
            'rules': [],
            'flows': [],
            'server_port': 9999,
            'version': 1
        }
        
        config_model = ConfigModel.model_validate(config_data)
        print("✓ ConfigModel 验证成功")
        print(f"  outbound 类型: {type(config_model.outbounds['test_print'])}")
        return True
    except Exception as e:
        print(f"✗ ConfigModel 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试 outbound 配置...")
    
    success = True
    success &= test_outbound_types()
    success &= test_print_config()
    success &= test_config_model()
    
    if success:
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ 部分测试失败")
        sys.exit(1)
